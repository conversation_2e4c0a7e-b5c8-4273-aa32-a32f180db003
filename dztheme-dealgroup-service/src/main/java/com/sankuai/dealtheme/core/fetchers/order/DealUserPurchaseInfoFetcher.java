package com.sankuai.dealtheme.core.fetchers.order;

import com.dianping.cat.Cat;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.graphql.Execution;
import com.sankuai.athena.graphql.ExecutionContext;
import com.sankuai.athena.graphql.FetchingContext;
import com.sankuai.athena.graphql.fetcher.BatchFetcher;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.theme.framework.annotations.Fetcher;
import com.sankuai.dealtheme.DealThemeConfig;
import com.sankuai.dealtheme.core.fetchers.model.DealModel;
import com.sankuai.dealtheme.core.fetchers.model.DealUserPurchaseInfoModel;
import com.sankuai.dealtheme.core.fetchers.utils.ContextUtils;
import com.sankuai.dealtheme.core.fetchers.utils.NodeUtils;
import com.sankuai.dealtheme.core.nr.atom.FacadeService;
import com.sankuai.dealtheme.core.utils.PlatformUtil;
import com.sankuai.dealtheme.themes.facade.LogControl;
import com.sankuai.general.order.querycenter.api.enums.BizLineEnum;
import com.sankuai.general.order.querycenter.api.enums.PlatformFlagEnum;
import com.sankuai.general.order.querycenter.api.enums.PlatformIndexKeyEnum;
import com.sankuai.general.order.querycenter.api.request.*;
import com.sankuai.general.order.querycenter.api.response.OrderSearchResponse;
import com.sankuai.general.order.querycenter.api.utis.OrderSearchAssistBuilderUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description : 商品关联的用户购买信息
 * @date : 2025/4/16
 */
@Fetcher(
        name = "DealUserPurchaseInfoFetcher",
        description = "用户180天内在门店下的商品购买次数，不区分平台",
        type = "Deal", field = "DealUserPurchaseInfoModel",
        params = {DealThemeConfig.DEAL_IDS_L, DealThemeConfig.PLATFORM, DealThemeConfig.USER_ID, DealThemeConfig.DP_SHOP_ID_FOR_LONG, DealThemeConfig.MT_SHOP_ID_FOR_LONG},
        needFields = {})
public class DealUserPurchaseInfoFetcher extends BatchFetcher<Integer, DealUserPurchaseInfoModel> {
    private static final Logger log = LoggerFactory.getLogger(DealUserPurchaseInfoFetcher.class);
    public static final String SPUG_ID = "spugid";
    @ConfigValue(key = "com.sankuai.dztheme.dealgroup.query.order.maxQuerySize", defaultValue = "1000")
    private int MAX_QUERY_SIZE;

    @Override
    public int batchSize() {
        return 200;
    }

    @Override
    public Integer batchKey(FetchingContext fetchingContext) {
        DealModel deal = fetchingContext.getSource();
        return deal.getDealId();
    }

    @Override
    public CompletableFuture<Map<Integer, DealUserPurchaseInfoModel>> batchGet(Map<Integer, FetchingContext> keyFetchContexts) {
        Execution execution = ContextUtils.getExecution(keyFetchContexts);
        ExecutionContext executionContext = ContextUtils.getExecutionContext(keyFetchContexts);
        if (execution == null || executionContext == null) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        Map<Integer, Integer> dp2mtDealIdMap = NodeUtils.getDp2MTDealIdMap(executionContext).join();
        return batchGetUserPurchaseInfo(dp2mtDealIdMap, execution);

    }

    private CompletableFuture<Map<Integer, DealUserPurchaseInfoModel>> batchGetUserPurchaseInfo(Map<Integer, Integer> dp2mtDealIdMap, Execution execution) {
        Collection<Integer> dpDealIds = dp2mtDealIdMap.keySet();  // 查询的时候只能通过点评dealId查询
        FacadeService facadeService = AthenaBeanFactory.getBean(FacadeService.class);
        Long mtUserId = getMtUserId(execution, facadeService);
        Long dpUserId = getDpUserId(execution, facadeService);
        Date now = new Date();
        OrderSearchRequest mtRequest = builderMtOrderSearchRequest(dpDealIds, execution, mtUserId, now);
        OrderSearchRequest dpRequest = builderDpOrderSearchRequest(dpDealIds, execution, dpUserId, now);
        SessionContext sessionContext = builderSessionRequest();
        boolean isMT = PlatformUtil.isMT(execution.getParameter(DealThemeConfig.PLATFORM));
        CompletableFuture<List<Map<String, String>>> mtOrderInfos = queryOrderPurchaseCount(execution, facadeService, mtRequest, sessionContext);
        CompletableFuture<List<Map<String, String>>> dpOrderInfos = queryOrderPurchaseCount(execution, facadeService, dpRequest, sessionContext);
        return CompletableFuture.allOf(mtOrderInfos, dpOrderInfos)
                .thenApply(v -> handlerOrderInfos(dp2mtDealIdMap, mtOrderInfos, dpOrderInfos, isMT, dpDealIds));
    }

    private Map<Integer, DealUserPurchaseInfoModel> handlerOrderInfos(Map<Integer, Integer> dp2mtDealIdMap, CompletableFuture<List<Map<String, String>>> mtOrderInfos, CompletableFuture<List<Map<String, String>>> dpOrderInfos, boolean isMT, Collection<Integer> dpDealIds) {
        List<Map<String, String>> mtOrders = mtOrderInfos.join();
        List<Map<String, String>> dpOrders = dpOrderInfos.join();
        List<Map<String, String>> allOrders = new ArrayList<>();
        if (!CollectionUtils.isEmpty(mtOrders)) {
            allOrders.addAll(mtOrders);
        }
        if (!CollectionUtils.isEmpty(dpOrders)) {
            allOrders.addAll(dpOrders);
        }
        if (CollectionUtils.isEmpty(allOrders)) {
            return initResult(dp2mtDealIdMap, isMT, dpDealIds);
        }
        Map<Integer, DealUserPurchaseInfoModel> result = initResult(dp2mtDealIdMap, isMT, dpDealIds);
        for (Map<String, String> order : allOrders) {
            if (order.containsKey(SPUG_ID)) {
                int dpDealId = NumberUtils.toInt(order.get(SPUG_ID));
                Integer targetDealId = isMT ? dp2mtDealIdMap.get(dpDealId) : dpDealId;
                result.computeIfPresent(targetDealId, (k, count) -> {
                    count.setDealPurchaseNums(count.getDealPurchaseNums() + 1);
                    return count;
                });
            }
        }
        return result;
    }

    private @NotNull CompletableFuture<List<Map<String, String>>> queryOrderPurchaseCount(Execution execution, FacadeService facadeService, OrderSearchRequest request, SessionContext sessionContext) {
        if (request == null) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        return facadeService.searchOrder(request, sessionContext)
                .thenApply(response -> {
                    logSearchOrderResponse(execution, request, response);
                    if (isInvalidResponse(response)) {
                        return Lists.<Map<String, String>>newArrayList();
                    }
                    long totalHit = response.getTotalHit();
                    if (totalHit <= MAX_QUERY_SIZE) {
                        return response.getData();
                    }
                    return handleLargeResultSet(request, response, sessionContext, facadeService, totalHit);
                });
    }

    private void logSearchOrderResponse(Execution execution, OrderSearchRequest request, OrderSearchResponse response) {
        LogControl.logFucByUser(execution, () -> log.info("DealUserPurchaseInfoFetcher.searchOrder,request:{},response:{}",
                JsonCodec.encode(request), JsonCodec.encode(response)));
    }

    private boolean isInvalidResponse(OrderSearchResponse response) {
        return response == null || CollectionUtils.isEmpty(response.getData());
    }

    private List<Map<String, String>> handleLargeResultSet(OrderSearchRequest request, OrderSearchResponse initResponse,
                                                           SessionContext sessionContext, FacadeService facadeService, long totalHit) {
        Cat.logEvent("DealUserPurchaseInfoFetcher", "searchOrderTooMany");
        List<CompletableFuture<OrderSearchResponse>> futures = Lists.newArrayListWithCapacity((int) Math.ceil(totalHit / (float) MAX_QUERY_SIZE));
        for (int pageNo = 2; pageNo <= Math.ceil(totalHit / (float) MAX_QUERY_SIZE); pageNo++) {
            OrderSearchRequest target = new OrderSearchRequest();
            BeanUtils.copyProperties(request, target);
            target.setPageNo(pageNo);
            futures.add(facadeService.searchOrder(target, sessionContext));
        }
        List<Map<String, String>> result = Lists.newArrayList(initResponse.getData());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<OrderSearchResponse> future : futures) {
            OrderSearchResponse response = future.join();
            if (response != null && !CollectionUtils.isEmpty(response.getData())) {
                result.addAll(response.getData());
            }
        }
        return result;
    }

    private Map<Integer, DealUserPurchaseInfoModel> initResult(Map<Integer, Integer> dp2mtDealIdMap, boolean isMT, Collection<Integer> dpDealIds) {
        return isMT ? buildDealUserPurchaseInfoModelResult(dp2mtDealIdMap.values()) : buildDealUserPurchaseInfoModelResult(dpDealIds);
    }

    private static Map<Integer, DealUserPurchaseInfoModel> buildDealUserPurchaseInfoModelResult(Collection<Integer> dealIds) {
        return dealIds.stream().collect(Collectors.toMap(Function.identity(), DealUserPurchaseInfoFetcher::buildDealUserPurchaseInfoModel));
    }

    private static DealUserPurchaseInfoModel buildDealUserPurchaseInfoModel(int mtDealId) {
        DealUserPurchaseInfoModel dealUserPurchaseInfoModel = new DealUserPurchaseInfoModel();
        dealUserPurchaseInfoModel.setDealId(mtDealId);
        dealUserPurchaseInfoModel.setDealPurchaseNums(0);
        return dealUserPurchaseInfoModel;
    }


    private static @NotNull SessionContext builderSessionRequest() {
        SessionContext sessionContext = new SessionContext();
        sessionContext.setBizLine(BizLineEnum.DISPLAY.getCode());
        return sessionContext;
    }

    private Long getMtUserId(Execution execution, FacadeService facadeService) {
        if (PlatformUtil.isMT(execution.getParameter(DealThemeConfig.PLATFORM))) {
            return execution.getParameter(DealThemeConfig.USER_ID);
        }
        return facadeService.getMtRealUserIdByDpUserId(execution.getParameter(DealThemeConfig.USER_ID)).join();
    }

    private Long getDpUserId(Execution execution, FacadeService facadeService) {
        if (PlatformUtil.isDP(execution.getParameter(DealThemeConfig.PLATFORM))) {
            return execution.getParameter(DealThemeConfig.USER_ID);
        }
        return facadeService.getDpRealUserIdByMtUserId(execution.getParameter(DealThemeConfig.USER_ID)).join();
    }


    private OrderSearchRequest builderMtOrderSearchRequest(Collection<Integer> dealIds, Execution execution, Long mtUserId, Date now) {
        if (mtUserId == null || mtUserId <= 0) {
            return null;
        }
        OrderSearchRequest request = new OrderSearchRequest();
        Date queryStartTime = DateUtils.addDays(now, -180);
        request.setPageNo(1);
        request.setPageSize(MAX_QUERY_SIZE);
        TermSearchRequest mtUserIdQuery = OrderSearchAssistBuilderUtils.createMtUserIdTermSearchRequest(Sets.newHashSet(String.valueOf(mtUserId)));
        TermSearchRequest mtShopIdQuery = OrderSearchAssistBuilderUtils.createPoiIdTermSearchRequest(Sets.newHashSet(String.valueOf((Long) execution.getParameter(DealThemeConfig.MT_SHOP_ID_FOR_LONG))));
        RangeSearchRequest createTimeQuery = OrderSearchAssistBuilderUtils.createCreateTimeRangeSearchRequest(String.valueOf(queryStartTime.getTime()), String.valueOf(now.getTime()));
        TermSearchRequest dealQuery = OrderSearchAssistBuilderUtils.createTermSearchRequest(PlatformIndexKeyEnum.SPUG_ID.getFieldName(), dealIds.stream().map(String::valueOf).collect(Collectors.toSet()));
        SortFieldRequest createTimeSort = OrderSearchAssistBuilderUtils.createCreateTimeSortFieldRequestDesc();

        OrderSearchDetailRequest mtSearch = new OrderSearchDetailRequest();
        mtSearch.setPlatformFlag(PlatformFlagEnum.MT.getPlatformCode());
        mtSearch.setTermSearchRequestList(Lists.newArrayList(mtUserIdQuery, mtShopIdQuery, dealQuery));
        mtSearch.setRangeSearchRequestList(Lists.newArrayList(createTimeQuery));
        ArrayList<OrderSearchDetailRequest> orderSearchDetailRequestList = Lists.newArrayList();
        orderSearchDetailRequestList.add(mtSearch);
        request.setOrderSearchDetailRequestList(orderSearchDetailRequestList);
        request.setSortFieldRequestList(Lists.newArrayList(createTimeSort));
        request.setReturnFields(Lists.newArrayList(PlatformIndexKeyEnum.SPUG_ID.getFieldName()));
        return request;
    }

    private OrderSearchRequest builderDpOrderSearchRequest(Collection<Integer> dealIds, Execution execution, Long dpUserId, Date now) {
        if (dpUserId == null || dpUserId <= 0) {
            return null;
        }
        OrderSearchRequest request = new OrderSearchRequest();
        Date queryStartTime = DateUtils.addDays(now, -180);
        request.setPageNo(1);
        request.setPageSize(MAX_QUERY_SIZE);
        TermSearchRequest dpUserIdQuery = OrderSearchAssistBuilderUtils.createDpUserIdTermSearchRequest(Sets.newHashSet(String.valueOf(dpUserId)));
        TermSearchRequest dpShopIdQuery = OrderSearchAssistBuilderUtils.createDpShopIdTermSearchRequest(Sets.newHashSet(String.valueOf((Long) execution.getParameter(DealThemeConfig.DP_SHOP_ID_FOR_LONG))));
        RangeSearchRequest createTimeQuery = OrderSearchAssistBuilderUtils.createCreateTimeRangeSearchRequest(String.valueOf(queryStartTime.getTime()), String.valueOf(now.getTime()));
        TermSearchRequest dealQuery = OrderSearchAssistBuilderUtils.createTermSearchRequest(PlatformIndexKeyEnum.SPUG_ID.getFieldName(), dealIds.stream().map(String::valueOf).collect(Collectors.toSet()));
        SortFieldRequest createTimeSort = OrderSearchAssistBuilderUtils.createCreateTimeSortFieldRequestDesc();

        OrderSearchDetailRequest dpSearch = new OrderSearchDetailRequest();
        dpSearch.setPlatformFlag(PlatformFlagEnum.DP.getPlatformCode());
        dpSearch.setTermSearchRequestList(Lists.newArrayList(dpUserIdQuery, dpShopIdQuery, dealQuery));
        dpSearch.setRangeSearchRequestList(Lists.newArrayList(createTimeQuery));
        ArrayList<OrderSearchDetailRequest> orderSearchDetailRequestList = Lists.newArrayList();
        orderSearchDetailRequestList.add(dpSearch);
        request.setOrderSearchDetailRequestList(orderSearchDetailRequestList);
        request.setSortFieldRequestList(Lists.newArrayList(createTimeSort));
        request.setReturnFields(Lists.newArrayList(PlatformIndexKeyEnum.SPUG_ID.getFieldName()));
        return request;
    }
}
